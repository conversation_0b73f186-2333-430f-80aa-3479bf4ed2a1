// Simple FBX Loader check - if this loads, we know the file system works
console.log('FBXLoader.js file loaded successfully');

// For now, let's just test if we can load files at all
// We'll add the actual FBXLoader code if needed

// Test function to check if we can access your character file
function testFileAccess() {
    console.log('Testing file access...');
    
    // Try to load your character file as a simple fetch test
    const testPath = 'Assets/player/Player model/make_a_male_character_0717071834_texture_fbx/make_a_male_character_0717071834_texture_fbx/make_a_male_character_0717071834_texture.fbx';
    
    fetch(testPath)
        .then(response => {
            if (response.ok) {
                console.log('✅ File access works! Your character file is accessible.');
                console.log('File size:', response.headers.get('content-length'), 'bytes');
            } else {
                console.error('❌ File access failed. Status:', response.status);
            }
        })
        .catch(error => {
            console.error('❌ File access error:', error);
            console.log('This might be a CORS issue - browser blocking local file access');
        });
}

// Run the test
testFileAccess();
