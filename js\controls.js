// Player Controls - WASD Movement, Space Jump, Mouse Look
class PlayerControls {
    constructor(player, camera) {
        this.player = player;
        this.camera = camera;
        this.enabled = false;
        
        // Input state
        this.keys = {
            forward: false,    // W
            backward: false,   // S
            left: false,       // A
            right: false,      // D
            jump: false,       // Space
            run: false         // Shift
        };
        
        // Mouse look
        this.mouseX = 0;
        this.mouseY = 0;
        this.mouseSensitivity = 0.002;
        this.maxPitch = Math.PI / 3; // 60 degrees up/down limit
        this.currentPitch = 0;
        this.currentYaw = 0;
        
        // Movement
        this.moveDirection = new THREE.Vector3();
        this.cameraDirection = new THREE.Vector3();
    }

    init() {
        this.setupEventListeners();
    }

    setupEventListeners() {
        // Keyboard events
        document.addEventListener('keydown', (event) => this.onKeyDown(event));
        document.addEventListener('keyup', (event) => this.onKeyUp(event));
        
        // Mouse events
        document.addEventListener('mousemove', (event) => this.onMouseMove(event));
        
        // Prevent context menu on right click
        document.addEventListener('contextmenu', (event) => event.preventDefault());
        
        // Click to regain pointer lock if lost
        document.addEventListener('click', () => {
            if (document.pointerLockElement !== document.body) {
                document.body.requestPointerLock();
            }
        });
    }

    onKeyDown(event) {
        if (!this.enabled) return;
        
        switch (event.code) {
            case 'KeyW':
                this.keys.forward = true;
                break;
            case 'KeyS':
                this.keys.backward = true;
                break;
            case 'KeyA':
                this.keys.left = true;
                break;
            case 'KeyD':
                this.keys.right = true;
                break;
            case 'Space':
                event.preventDefault();
                this.keys.jump = true;
                break;
            case 'ShiftLeft':
            case 'ShiftRight':
                this.keys.run = true;
                break;
            case 'Escape':
                // Exit pointer lock
                document.exitPointerLock();
                break;
        }
    }

    onKeyUp(event) {
        if (!this.enabled) return;
        
        switch (event.code) {
            case 'KeyW':
                this.keys.forward = false;
                break;
            case 'KeyS':
                this.keys.backward = false;
                break;
            case 'KeyA':
                this.keys.left = false;
                break;
            case 'KeyD':
                this.keys.right = false;
                break;
            case 'Space':
                this.keys.jump = false;
                break;
            case 'ShiftLeft':
            case 'ShiftRight':
                this.keys.run = false;
                break;
        }
    }

    onMouseMove(event) {
        if (!this.enabled || document.pointerLockElement !== document.body) return;
        
        // Update mouse movement
        this.mouseX = event.movementX || 0;
        this.mouseY = event.movementY || 0;
        
        // Update camera rotation
        this.currentYaw -= this.mouseX * this.mouseSensitivity;
        this.currentPitch -= this.mouseY * this.mouseSensitivity;
        
        // Clamp pitch to prevent over-rotation
        this.currentPitch = Math.max(-this.maxPitch, Math.min(this.maxPitch, this.currentPitch));
    }

    update(deltaTime) {
        if (!this.enabled) return;
        
        this.updateMovement();
        this.updateCameraRotation();
        this.handleJump();
    }

    updateMovement() {
        // Reset movement direction
        this.moveDirection.set(0, 0, 0);
        
        // Calculate movement direction based on camera orientation
        const forward = new THREE.Vector3(0, 0, -1);
        const right = new THREE.Vector3(1, 0, 0);
        
        // Apply camera yaw rotation to movement vectors
        forward.applyAxisAngle(new THREE.Vector3(0, 1, 0), this.currentYaw);
        right.applyAxisAngle(new THREE.Vector3(0, 1, 0), this.currentYaw);
        
        // Build movement direction based on input
        if (this.keys.forward) {
            this.moveDirection.add(forward);
        }
        if (this.keys.backward) {
            this.moveDirection.sub(forward);
        }
        if (this.keys.left) {
            this.moveDirection.sub(right);
        }
        if (this.keys.right) {
            this.moveDirection.add(right);
        }
        
        // Normalize movement direction
        if (this.moveDirection.length() > 0) {
            this.moveDirection.normalize();
        }
        
        // Apply movement to player
        this.player.move(this.moveDirection, this.keys.run);
    }

    updateCameraRotation() {
        // Create rotation quaternion for camera
        const yawQuaternion = new THREE.Quaternion();
        yawQuaternion.setFromAxisAngle(new THREE.Vector3(0, 1, 0), this.currentYaw);
        
        const pitchQuaternion = new THREE.Quaternion();
        pitchQuaternion.setFromAxisAngle(new THREE.Vector3(1, 0, 0), this.currentPitch);
        
        // Combine rotations
        const cameraQuaternion = new THREE.Quaternion();
        cameraQuaternion.multiplyQuaternions(yawQuaternion, pitchQuaternion);
        
        // Apply rotation to camera offset
        const playerPosition = this.player.getPosition();
        const cameraOffset = new THREE.Vector3(0, 5, 8);
        cameraOffset.applyQuaternion(yawQuaternion);
        cameraOffset.add(playerPosition);
        
        // Update camera position and look target
        this.camera.position.lerp(cameraOffset, 0.1);
        
        const lookTarget = playerPosition.clone();
        lookTarget.y += 2; // Look at player's chest level
        this.camera.lookAt(lookTarget);
    }

    handleJump() {
        if (this.keys.jump) {
            this.player.jump();
            this.keys.jump = false; // Prevent continuous jumping
        }
    }

    // Utility methods
    isMoving() {
        return this.keys.forward || this.keys.backward || this.keys.left || this.keys.right;
    }

    isRunning() {
        return this.keys.run && this.isMoving();
    }

    getMovementDirection() {
        return this.moveDirection.clone();
    }

    // Enable/disable controls
    enable() {
        this.enabled = true;
    }

    disable() {
        this.enabled = false;
        // Reset all keys
        Object.keys(this.keys).forEach(key => {
            this.keys[key] = false;
        });
    }

    // Reset camera rotation
    resetCamera() {
        this.currentPitch = 0;
        this.currentYaw = 0;
    }
}
