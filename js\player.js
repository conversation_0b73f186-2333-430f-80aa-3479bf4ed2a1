// Player Class with Third Person Movement and Animation
class Player {
    constructor(scene, camera) {
        this.scene = scene;
        this.camera = camera;
        this.mesh = null;
        this.mixer = null;
        this.animations = {};
        this.currentAnimation = null;
        
        // Movement properties
        this.position = new THREE.Vector3(0, 0, 0);
        this.velocity = new THREE.Vector3(0, 0, 0);
        this.direction = new THREE.Vector3(0, 0, 0);
        this.speed = 5;
        this.runSpeed = 8;
        this.jumpForce = 10;
        this.gravity = -25;
        this.isGrounded = false;
        this.isRunning = false;
        this.isMoving = false;
        
        // Camera offset for third person
        this.cameraOffset = new THREE.Vector3(0, 5, 8);
        this.cameraTarget = new THREE.Vector3(0, 2, 0);
        
        // Collision
        this.boundingBox = new THREE.Box3();
        this.height = 1.8;
        this.radius = 0.5;
    }

    async init() {
        console.log('Initializing player...');
        await this.loadModel();
        await this.loadAnimations();
        this.setupCollision();
        this.playAnimation('idle');
        console.log('Player initialized successfully');
    }

    async loadModel() {
        return new Promise((resolve, reject) => {
            // Check if FBXLoader is available
            if (typeof THREE.FBXLoader === 'undefined') {
                console.warn('FBXLoader not available, using placeholder');
                this.createPlaceholderPlayer();
                resolve();
                return;
            }

            const loader = new THREE.FBXLoader();

            // Load the main character model
            loader.load('Assets/player/Player model/make_a_male_character_0717071834_texture_fbx/make_a_male_character_0717071834_texture_fbx.fbx',
                (object) => {
                    this.mesh = object;
                    this.mesh.scale.setScalar(0.01); // Scale down if needed
                    this.mesh.position.copy(this.position);

                    // Enable shadows
                    this.mesh.traverse((child) => {
                        if (child.isMesh) {
                            child.castShadow = true;
                            child.receiveShadow = true;
                        }
                    });

                    this.scene.add(this.mesh);

                    // Setup animation mixer
                    this.mixer = new THREE.AnimationMixer(this.mesh);

                    resolve();
                },
                (progress) => {
                    console.log('Loading player model:', (progress.loaded / progress.total * 100) + '%');
                },
                (error) => {
                    console.error('Error loading player model:', error);
                    // Create a simple placeholder if model fails to load
                    this.createPlaceholderPlayer();
                    resolve();
                }
            );
        });
    }

    createPlaceholderPlayer() {
        console.log('Creating placeholder player...');
        // Create a simple cylinder as placeholder (capsule might not be available)
        const geometry = new THREE.CylinderGeometry(this.radius, this.radius, this.height, 8);
        const material = new THREE.MeshLambertMaterial({ color: 0x00ff00 });
        this.mesh = new THREE.Mesh(geometry, material);
        this.mesh.position.copy(this.position);
        this.mesh.position.y = this.height / 2; // Lift it up so it sits on ground
        this.mesh.castShadow = true;
        this.scene.add(this.mesh);
        console.log('Placeholder player created and added to scene at position:', this.mesh.position);

        // Create a simple mixer for placeholder
        this.mixer = new THREE.AnimationMixer(this.mesh);
    }

    async loadAnimations() {
        // Skip animation loading if FBXLoader is not available
        if (typeof THREE.FBXLoader === 'undefined') {
            console.warn('FBXLoader not available, skipping animations');
            return;
        }

        const animationPaths = {
            'idle': 'Assets/player/Animations/Injured/injured idle.fbx',
            'walk': 'Assets/player/Animations/Character_movment/Walking.fbx',
            'run': 'Assets/player/Animations/Character_movment/Running.fbx',
            'jump_up': 'Assets/player/Animations/Character_movment/Jumping Up.fbx',
            'jump_down': 'Assets/player/Animations/Character_movment/Jumping Down.fbx',
            'run_backward': 'Assets/player/Animations/Character_movment/Running Backward.fbx'
        };

        const loader = new THREE.FBXLoader();

        for (const [name, path] of Object.entries(animationPaths)) {
            try {
                const animationFBX = await this.loadAnimation(loader, path);
                if (animationFBX && animationFBX.animations.length > 0) {
                    const action = this.mixer.clipAction(animationFBX.animations[0]);
                    this.animations[name] = action;
                }
            } catch (error) {
                console.warn(`Failed to load animation ${name}:`, error);
            }
        }
    }

    loadAnimation(loader, path) {
        return new Promise((resolve, reject) => {
            loader.load(path, resolve, undefined, reject);
        });
    }

    setupCollision() {
        this.boundingBox.setFromCenterAndSize(
            this.position,
            new THREE.Vector3(this.radius * 2, this.height, this.radius * 2)
        );
    }

    playAnimation(name, loop = true) {
        if (this.currentAnimation) {
            this.currentAnimation.fadeOut(0.2);
        }
        
        if (this.animations[name]) {
            this.currentAnimation = this.animations[name];
            this.currentAnimation.reset();
            this.currentAnimation.setLoop(loop ? THREE.LoopRepeat : THREE.LoopOnce);
            this.currentAnimation.fadeIn(0.2);
            this.currentAnimation.play();
        }
    }

    update(deltaTime) {
        if (!this.mesh) return;
        
        // Update animation mixer
        if (this.mixer) {
            this.mixer.update(deltaTime);
        }
        
        // Apply gravity
        if (!this.isGrounded) {
            this.velocity.y += this.gravity * deltaTime;
        }
        
        // Update position
        this.position.add(this.velocity.clone().multiplyScalar(deltaTime));
        
        // Ground collision (simple)
        if (this.position.y <= 0) {
            this.position.y = 0;
            this.velocity.y = 0;
            this.isGrounded = true;
        } else {
            this.isGrounded = false;
        }
        
        // Update mesh position
        this.mesh.position.copy(this.position);
        
        // Update bounding box
        this.boundingBox.setFromCenterAndSize(
            this.position,
            new THREE.Vector3(this.radius * 2, this.height, this.radius * 2)
        );
        
        // Update camera position (third person)
        this.updateCamera();
        
        // Update animation based on movement state
        this.updateAnimationState();
    }

    updateCamera() {
        const idealOffset = this.cameraOffset.clone();
        idealOffset.applyQuaternion(this.mesh.quaternion);
        idealOffset.add(this.position);
        
        const idealTarget = this.cameraTarget.clone();
        idealTarget.add(this.position);
        
        // Smooth camera movement
        this.camera.position.lerp(idealOffset, 0.1);
        this.camera.lookAt(idealTarget);
    }

    updateAnimationState() {
        let targetAnimation = 'idle';
        
        if (this.isMoving) {
            if (this.isRunning) {
                targetAnimation = 'run';
            } else {
                targetAnimation = 'walk';
            }
        }
        
        if (!this.isGrounded && this.velocity.y > 0) {
            targetAnimation = 'jump_up';
        } else if (!this.isGrounded && this.velocity.y < 0) {
            targetAnimation = 'jump_down';
        }
        
        // Only change animation if it's different
        if (!this.currentAnimation || this.currentAnimation !== this.animations[targetAnimation]) {
            this.playAnimation(targetAnimation);
        }
    }

    move(direction, isRunning = false) {
        this.isMoving = direction.length() > 0;
        this.isRunning = isRunning;
        
        if (this.isMoving) {
            const speed = isRunning ? this.runSpeed : this.speed;
            this.velocity.x = direction.x * speed;
            this.velocity.z = direction.z * speed;
            
            // Rotate player to face movement direction
            const angle = Math.atan2(direction.x, direction.z);
            this.mesh.rotation.y = angle;
        } else {
            this.velocity.x = 0;
            this.velocity.z = 0;
        }
    }

    jump() {
        if (this.isGrounded) {
            this.velocity.y = this.jumpForce;
            this.isGrounded = false;
        }
    }

    getPosition() {
        return this.position.clone();
    }

    setPosition(x, y, z) {
        this.position.set(x, y, z);
        if (this.mesh) {
            this.mesh.position.copy(this.position);
        }
    }
}
