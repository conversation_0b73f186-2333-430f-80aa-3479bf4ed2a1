// World Generation and Management
class World {
    constructor(scene) {
        this.scene = scene;
        this.terrain = null;
        this.objects = [];
        this.buildings = [];
        this.trees = [];
    }

    generate() {
        this.createTerrain();
        this.createSkybox();
        this.loadWorldAssets();
    }

    createTerrain() {
        // Create a large plane for the ground
        const terrainSize = 200;
        const terrainGeometry = new THREE.PlaneGeometry(terrainSize, terrainSize, 50, 50);
        
        // Add some height variation to make it more interesting
        const vertices = terrainGeometry.attributes.position.array;
        for (let i = 0; i < vertices.length; i += 3) {
            // Add random height variation
            vertices[i + 2] = Math.random() * 2 - 1; // Y coordinate (height)
        }
        terrainGeometry.attributes.position.needsUpdate = true;
        terrainGeometry.computeVertexNormals();
        
        // Create terrain material
        const terrainMaterial = new THREE.MeshLambertMaterial({ 
            color: 0x4a5d23, // Forest green
            side: THREE.DoubleSide
        });
        
        this.terrain = new THREE.Mesh(terrainGeometry, terrainMaterial);
        this.terrain.rotation.x = -Math.PI / 2; // Rotate to be horizontal
        this.terrain.receiveShadow = true;
        this.scene.add(this.terrain);
        
        // Add some grass texture if available
        this.addGrassTexture();
    }

    addGrassTexture() {
        // Try to load grass texture, fallback to solid color if not available
        const textureLoader = new THREE.TextureLoader();
        
        // You can add grass texture here if you have one
        // For now, we'll create a simple procedural grass-like appearance
        const canvas = document.createElement('canvas');
        canvas.width = 256;
        canvas.height = 256;
        const ctx = canvas.getContext('2d');
        
        // Create grass-like pattern
        ctx.fillStyle = '#4a5d23';
        ctx.fillRect(0, 0, 256, 256);
        
        // Add some variation
        for (let i = 0; i < 1000; i++) {
            ctx.fillStyle = `hsl(${80 + Math.random() * 40}, 60%, ${20 + Math.random() * 30}%)`;
            ctx.fillRect(Math.random() * 256, Math.random() * 256, 2, 2);
        }
        
        const grassTexture = new THREE.CanvasTexture(canvas);
        grassTexture.wrapS = THREE.RepeatWrapping;
        grassTexture.wrapT = THREE.RepeatWrapping;
        grassTexture.repeat.set(20, 20);
        
        this.terrain.material.map = grassTexture;
        this.terrain.material.needsUpdate = true;
    }

    createSkybox() {
        // Create a simple gradient sky
        const skyGeometry = new THREE.SphereGeometry(500, 32, 32);
        const skyMaterial = new THREE.MeshBasicMaterial({
            color: 0x87CEEB,
            side: THREE.BackSide,
            fog: false
        });
        
        const sky = new THREE.Mesh(skyGeometry, skyMaterial);
        this.scene.add(sky);
    }

    async loadWorldAssets() {
        // Load trees, rocks, and other nature assets
        await this.loadTrees();
        await this.loadRocks();
        await this.loadBuildings();
        await this.loadObjects();
    }

    async loadTrees() {
        // Create simple tree placeholders for now
        // Later we can load actual tree models from Assets/World/Terrain/Nature/trees
        this.createSimpleTrees();
    }

    createSimpleTrees() {
        const treeCount = 50;
        
        for (let i = 0; i < treeCount; i++) {
            const tree = this.createSimpleTree();
            
            // Random position within terrain bounds
            const x = (Math.random() - 0.5) * 180;
            const z = (Math.random() - 0.5) * 180;
            const y = 0;
            
            tree.position.set(x, y, z);
            this.scene.add(tree);
            this.trees.push(tree);
        }
    }

    createSimpleTree() {
        const treeGroup = new THREE.Group();
        
        // Tree trunk
        const trunkGeometry = new THREE.CylinderGeometry(0.3, 0.5, 4, 8);
        const trunkMaterial = new THREE.MeshLambertMaterial({ color: 0x8B4513 });
        const trunk = new THREE.Mesh(trunkGeometry, trunkMaterial);
        trunk.position.y = 2;
        trunk.castShadow = true;
        trunk.receiveShadow = true;
        treeGroup.add(trunk);
        
        // Tree leaves
        const leavesGeometry = new THREE.SphereGeometry(2.5, 8, 8);
        const leavesMaterial = new THREE.MeshLambertMaterial({ color: 0x228B22 });
        const leaves = new THREE.Mesh(leavesGeometry, leavesMaterial);
        leaves.position.y = 5;
        leaves.castShadow = true;
        leaves.receiveShadow = true;
        treeGroup.add(leaves);
        
        return treeGroup;
    }

    async loadRocks() {
        // Create simple rock placeholders
        this.createSimpleRocks();
    }

    createSimpleRocks() {
        const rockCount = 20;
        
        for (let i = 0; i < rockCount; i++) {
            const rock = this.createSimpleRock();
            
            // Random position
            const x = (Math.random() - 0.5) * 160;
            const z = (Math.random() - 0.5) * 160;
            const y = 0;
            
            rock.position.set(x, y, z);
            this.scene.add(rock);
            this.objects.push(rock);
        }
    }

    createSimpleRock() {
        const rockGeometry = new THREE.DodecahedronGeometry(Math.random() * 1.5 + 0.5);
        const rockMaterial = new THREE.MeshLambertMaterial({ color: 0x696969 });
        const rock = new THREE.Mesh(rockGeometry, rockMaterial);
        
        rock.castShadow = true;
        rock.receiveShadow = true;
        
        // Random rotation
        rock.rotation.set(
            Math.random() * Math.PI,
            Math.random() * Math.PI,
            Math.random() * Math.PI
        );
        
        return rock;
    }

    async loadBuildings() {
        // Try to load the cottage model
        try {
            const cottage = await this.loadCottage();
            if (cottage) {
                cottage.position.set(20, 0, 20);
                cottage.scale.setScalar(0.1); // Scale down if needed
                this.scene.add(cottage);
                this.buildings.push(cottage);
            }
        } catch (error) {
            console.warn('Could not load cottage model:', error);
            // Create a simple house placeholder
            this.createSimpleHouse();
        }
    }

    loadCottage() {
        return new Promise((resolve, reject) => {
            if (typeof THREE.FBXLoader === 'undefined') {
                reject(new Error('FBXLoader not available'));
                return;
            }

            const loader = new THREE.FBXLoader();
            loader.load(
                'Assets/World/Buildings/Snow covered CottageFBX.fbx',
                (object) => {
                    object.traverse((child) => {
                        if (child.isMesh) {
                            child.castShadow = true;
                            child.receiveShadow = true;
                        }
                    });
                    resolve(object);
                },
                undefined,
                reject
            );
        });
    }

    createSimpleHouse() {
        const houseGroup = new THREE.Group();
        
        // House base
        const baseGeometry = new THREE.BoxGeometry(6, 4, 8);
        const baseMaterial = new THREE.MeshLambertMaterial({ color: 0x8B4513 });
        const base = new THREE.Mesh(baseGeometry, baseMaterial);
        base.position.y = 2;
        base.castShadow = true;
        base.receiveShadow = true;
        houseGroup.add(base);
        
        // Roof
        const roofGeometry = new THREE.ConeGeometry(5, 3, 4);
        const roofMaterial = new THREE.MeshLambertMaterial({ color: 0x654321 });
        const roof = new THREE.Mesh(roofGeometry, roofMaterial);
        roof.position.y = 5.5;
        roof.rotation.y = Math.PI / 4;
        roof.castShadow = true;
        houseGroup.add(roof);
        
        houseGroup.position.set(20, 0, 20);
        this.scene.add(houseGroup);
        this.buildings.push(houseGroup);
    }

    async loadObjects() {
        // Load barrels and other objects
        this.createSimpleObjects();
    }

    createSimpleObjects() {
        // Create some barrels around the world
        for (let i = 0; i < 10; i++) {
            const barrel = this.createSimpleBarrel();
            
            const x = (Math.random() - 0.5) * 100;
            const z = (Math.random() - 0.5) * 100;
            barrel.position.set(x, 0, z);
            
            this.scene.add(barrel);
            this.objects.push(barrel);
        }
    }

    createSimpleBarrel() {
        const barrelGeometry = new THREE.CylinderGeometry(0.8, 0.9, 1.5, 12);
        const barrelMaterial = new THREE.MeshLambertMaterial({ color: 0x8B4513 });
        const barrel = new THREE.Mesh(barrelGeometry, barrelMaterial);
        
        barrel.castShadow = true;
        barrel.receiveShadow = true;
        
        return barrel;
    }

    // Collision detection for world objects
    checkCollisions(playerBoundingBox) {
        const collisions = [];
        
        // Check against all objects
        [...this.objects, ...this.buildings, ...this.trees].forEach(object => {
            if (object.geometry) {
                const objectBox = new THREE.Box3().setFromObject(object);
                if (playerBoundingBox.intersectsBox(objectBox)) {
                    collisions.push(object);
                }
            }
        });
        
        return collisions;
    }

    // Get terrain height at position (for more advanced terrain following)
    getTerrainHeight(x, z) {
        // For now, return 0 (flat terrain)
        // Later this can be enhanced to sample actual terrain height
        return 0;
    }
}
