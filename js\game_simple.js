// Minimal Game - Just Plane and Player
let scene, camera, renderer, player;
let playerPosition = new THREE.Vector3(0, 1, 0);
let playerVelocity = new THREE.Vector3(0, 0, 0);
let isGrounded = true;
let cameraYaw = 0;
let cameraPitch = 0;
let pointerLocked = false;
let clock = new THREE.Clock();

let keys = {
    forward: false,
    backward: false,
    left: false,
    right: false,
    jump: false,
    run: false
};

function initGame() {
    console.log('Starting minimal game...');
    
    // Create scene
    scene = new THREE.Scene();
    scene.background = new THREE.Color(0x87CEEB); // Sky blue
    console.log('Scene created');
    
    // Create camera
    camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
    camera.position.set(0, 5, 10);
    camera.lookAt(0, 0, 0);
    console.log('Camera created');
    
    // Create renderer
    renderer = new THREE.WebGLRenderer({ antialias: true });
    renderer.setSize(window.innerWidth, window.innerHeight);
    renderer.setClearColor(0x87CEEB);
    
    // Add canvas to page
    const canvas = renderer.domElement;
    canvas.style.position = 'absolute';
    canvas.style.top = '0';
    canvas.style.left = '0';
    canvas.style.zIndex = '5';
    document.getElementById('container').appendChild(canvas);
    console.log('Renderer created and added to page');
    
    // Create simple ground plane
    const groundGeometry = new THREE.PlaneGeometry(50, 50);
    const groundMaterial = new THREE.MeshBasicMaterial({ color: 0x4a5d23 });
    const ground = new THREE.Mesh(groundGeometry, groundMaterial);
    ground.rotation.x = -Math.PI / 2; // Make it horizontal
    scene.add(ground);
    console.log('Ground plane created');
    
    // Create simple player (green box)
    const playerGeometry = new THREE.BoxGeometry(1, 2, 1);
    const playerMaterial = new THREE.MeshBasicMaterial({ color: 0x00ff00 });
    player = new THREE.Mesh(playerGeometry, playerMaterial);
    player.position.copy(playerPosition);
    scene.add(player);
    console.log('Player created');
    
    // Add some lighting
    const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
    scene.add(ambientLight);
    
    const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
    directionalLight.position.set(10, 10, 5);
    scene.add(directionalLight);
    console.log('Lighting added');
    
    // Setup controls
    setupControls();
    
    // Show game UI
    document.getElementById('gameCanvas').style.display = 'none'; // Hide the old canvas
    document.getElementById('gameUI').style.display = 'block';
    document.getElementById('crosshair').style.display = 'block';
    
    // Start animation loop
    animate();
    
    console.log('Game initialized successfully!');
    
    // Request pointer lock after a delay
    setTimeout(() => {
        document.body.requestPointerLock();
    }, 1000);
}

function setupControls() {
    // Keyboard controls
    document.addEventListener('keydown', onKeyDown);
    document.addEventListener('keyup', onKeyUp);
    document.addEventListener('mousemove', onMouseMove);
    
    // Pointer lock
    document.addEventListener('pointerlockchange', () => {
        pointerLocked = document.pointerLockElement === document.body;
    });
    
    console.log('Controls setup complete');
}

function onKeyDown(event) {
    switch (event.code) {
        case 'KeyW': keys.forward = true; break;
        case 'KeyS': keys.backward = true; break;
        case 'KeyA': keys.left = true; break;
        case 'KeyD': keys.right = true; break;
        case 'Space': 
            event.preventDefault();
            keys.jump = true; 
            break;
        case 'ShiftLeft':
        case 'ShiftRight': 
            keys.run = true; 
            break;
        case 'Escape':
            document.exitPointerLock();
            break;
    }
}

function onKeyUp(event) {
    switch (event.code) {
        case 'KeyW': keys.forward = false; break;
        case 'KeyS': keys.backward = false; break;
        case 'KeyA': keys.left = false; break;
        case 'KeyD': keys.right = false; break;
        case 'Space': keys.jump = false; break;
        case 'ShiftLeft':
        case 'ShiftRight': keys.run = false; break;
    }
}

function onMouseMove(event) {
    if (!pointerLocked) return;
    
    const sensitivity = 0.002;
    cameraYaw -= event.movementX * sensitivity;
    cameraPitch -= event.movementY * sensitivity;
    
    // Clamp pitch
    cameraPitch = Math.max(-Math.PI/3, Math.min(Math.PI/3, cameraPitch));
}

function updatePlayer(deltaTime) {
    if (!player) return;
    
    // Movement
    const moveDirection = new THREE.Vector3();
    const speed = keys.run ? 8 : 5;
    
    if (keys.forward) moveDirection.z -= 1;
    if (keys.backward) moveDirection.z += 1;
    if (keys.left) moveDirection.x -= 1;
    if (keys.right) moveDirection.x += 1;
    
    // Normalize and apply speed
    if (moveDirection.length() > 0) {
        moveDirection.normalize();
        
        // Apply camera rotation to movement
        moveDirection.applyAxisAngle(new THREE.Vector3(0, 1, 0), cameraYaw);
        
        playerVelocity.x = moveDirection.x * speed;
        playerVelocity.z = moveDirection.z * speed;
    } else {
        playerVelocity.x = 0;
        playerVelocity.z = 0;
    }
    
    // Jumping
    if (keys.jump && isGrounded) {
        playerVelocity.y = 10;
        isGrounded = false;
        keys.jump = false; // Prevent continuous jumping
    }
    
    // Gravity
    if (!isGrounded) {
        playerVelocity.y -= 25 * deltaTime;
    }
    
    // Update position
    playerPosition.add(playerVelocity.clone().multiplyScalar(deltaTime));
    
    // Ground collision
    if (playerPosition.y <= 1) {
        playerPosition.y = 1;
        playerVelocity.y = 0;
        isGrounded = true;
    }
    
    // Update player mesh position
    player.position.copy(playerPosition);
}

function updateCamera() {
    if (!player) return;
    
    // Third person camera
    const cameraDistance = 8;
    const cameraHeight = 5;
    
    const cameraOffset = new THREE.Vector3(
        Math.sin(cameraYaw) * cameraDistance,
        cameraHeight + Math.sin(cameraPitch) * 3,
        Math.cos(cameraYaw) * cameraDistance
    );
    
    camera.position.copy(playerPosition).add(cameraOffset);
    camera.lookAt(playerPosition.clone().add(new THREE.Vector3(0, 2, 0)));
}

function updateUI() {
    const positionElement = document.getElementById('playerPosition');
    if (positionElement) {
        positionElement.textContent = `${playerPosition.x.toFixed(1)}, ${playerPosition.y.toFixed(1)}, ${playerPosition.z.toFixed(1)}`;
    }
}

function animate() {
    requestAnimationFrame(animate);
    
    const deltaTime = clock.getDelta();
    
    updatePlayer(deltaTime);
    updateCamera();
    updateUI();
    
    renderer.render(scene, camera);
}

// Handle window resize
window.addEventListener('resize', () => {
    camera.aspect = window.innerWidth / window.innerHeight;
    camera.updateProjectionMatrix();
    renderer.setSize(window.innerWidth, window.innerHeight);
});
