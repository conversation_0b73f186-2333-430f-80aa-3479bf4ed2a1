// Main Game Controller
class Game {
    constructor() {
        this.scene = null;
        this.camera = null;
        this.renderer = null;
        this.player = null;
        this.world = null;
        this.clock = new THREE.Clock();
        this.isGameRunning = false;
    }

    async init() {
        try {
            console.log('Initializing game...');
            this.setupRenderer();
            this.setupScene();
            this.setupCamera();
            this.setupLighting();

            console.log('Setting up world...');
            this.setupWorld();

            console.log('Setting up player...');
            await this.setupPlayer();

            console.log('Setting up controls...');
            this.setupControls();
            this.setupEventListeners();

            console.log('Starting animation loop...');
            this.animate();
            this.isGameRunning = true;

            // Show game UI
            document.getElementById('gameCanvas').style.display = 'block';
            document.getElementById('gameUI').style.display = 'block';
            document.getElementById('crosshair').style.display = 'block';

            console.log('Game initialized successfully!');

            // Lock pointer for mouse look after a short delay
            setTimeout(() => {
                document.body.requestPointerLock();
            }, 500);

        } catch (error) {
            console.error('Error initializing game:', error);
        }
    }

    setupRenderer() {
        this.renderer = new THREE.WebGLRenderer({ antialias: true });
        this.renderer.setSize(window.innerWidth, window.innerHeight);
        this.renderer.setClearColor(0x87CEEB); // Sky blue
        this.renderer.shadowMap.enabled = true;
        this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
        
        const canvas = this.renderer.domElement;
        canvas.id = 'gameCanvas';
        document.getElementById('container').appendChild(canvas);
    }

    setupScene() {
        this.scene = new THREE.Scene();
        this.scene.fog = new THREE.Fog(0x87CEEB, 50, 200);

        // Add a test cube to make sure rendering is working
        const testGeometry = new THREE.BoxGeometry(2, 2, 2);
        const testMaterial = new THREE.MeshLambertMaterial({ color: 0xff0000 });
        const testCube = new THREE.Mesh(testGeometry, testMaterial);
        testCube.position.set(5, 1, 0);
        this.scene.add(testCube);
        console.log('Test cube added to scene');
    }

    setupCamera() {
        this.camera = new THREE.PerspectiveCamera(
            75,
            window.innerWidth / window.innerHeight,
            0.1,
            1000
        );
        this.camera.position.set(0, 10, 15);
        this.camera.lookAt(0, 0, 0);
        console.log('Camera positioned at:', this.camera.position);
    }

    setupLighting() {
        // Ambient light
        const ambientLight = new THREE.AmbientLight(0x404040, 0.4);
        this.scene.add(ambientLight);

        // Directional light (sun)
        const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
        directionalLight.position.set(50, 50, 50);
        directionalLight.castShadow = true;
        directionalLight.shadow.mapSize.width = 2048;
        directionalLight.shadow.mapSize.height = 2048;
        directionalLight.shadow.camera.near = 0.5;
        directionalLight.shadow.camera.far = 500;
        directionalLight.shadow.camera.left = -50;
        directionalLight.shadow.camera.right = 50;
        directionalLight.shadow.camera.top = 50;
        directionalLight.shadow.camera.bottom = -50;
        this.scene.add(directionalLight);
    }

    setupWorld() {
        this.world = new World(this.scene);
        this.world.generate();
    }

    async setupPlayer() {
        this.player = new Player(this.scene, this.camera);
        await this.player.init();
    }

    setupControls() {
        this.controls = new PlayerControls(this.player, this.camera);
        this.controls.init();
    }

    setupEventListeners() {
        window.addEventListener('resize', () => this.onWindowResize());
        
        // Pointer lock change
        document.addEventListener('pointerlockchange', () => {
            if (document.pointerLockElement === document.body) {
                this.controls.enabled = true;
            } else {
                this.controls.enabled = false;
            }
        });
    }

    onWindowResize() {
        this.camera.aspect = window.innerWidth / window.innerHeight;
        this.camera.updateProjectionMatrix();
        this.renderer.setSize(window.innerWidth, window.innerHeight);
    }

    animate() {
        if (!this.isGameRunning) return;
        
        requestAnimationFrame(() => this.animate());
        
        const deltaTime = this.clock.getDelta();
        
        if (this.player) {
            this.player.update(deltaTime);
        }
        
        if (this.controls) {
            this.controls.update(deltaTime);
        }

        // Update UI
        this.updateUI();

        this.renderer.render(this.scene, this.camera);
    }

    updateUI() {
        if (this.player) {
            const pos = this.player.getPosition();
            const positionElement = document.getElementById('playerPosition');
            if (positionElement) {
                positionElement.textContent = `${pos.x.toFixed(1)}, ${pos.y.toFixed(1)}, ${pos.z.toFixed(1)}`;
            }
        }
    }

    destroy() {
        this.isGameRunning = false;
        if (this.renderer) {
            this.renderer.dispose();
        }
    }
}

// Global game instance
let game = null;

// Initialize game function called from menu
async function initGame() {
    if (game) {
        game.destroy();
    }
    game = new Game();
    await game.init();
}
