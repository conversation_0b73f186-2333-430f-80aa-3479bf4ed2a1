// Character Game with Animations
let scene, camera, renderer, player, mixer;
let playerPosition = new THREE.Vector3(0, 1, 0);
let playerVelocity = new THREE.Vector3(0, 0, 0);
let isGrounded = true;
let cameraYaw = 0;
let cameraPitch = 0;
let pointerLocked = false;
let clock = new THREE.Clock();

// Animation system
let animations = {};
let currentAction = null;
let isPlayerLoaded = false;

let keys = {
    forward: false,
    backward: false,
    left: false,
    right: false,
    jump: false,
    run: false,
    crouch: false
};

function initGame() {
    console.log('Starting minimal game...');

    // Check if THREE is available
    if (typeof THREE === 'undefined') {
        console.error('THREE.js not loaded!');
        alert('THREE.js failed to load. Please check your internet connection.');
        return;
    }

    try {
        // Create scene
        scene = new THREE.Scene();
        scene.background = new THREE.Color(0x87CEEB); // Sky blue
        console.log('Scene created');

        // Create camera
        camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
        camera.position.set(0, 5, 10);
        camera.lookAt(0, 0, 0);
        console.log('Camera created');

        // Create renderer
        renderer = new THREE.WebGLRenderer({ antialias: true });
        renderer.setSize(window.innerWidth, window.innerHeight);
        renderer.setClearColor(0x87CEEB);

        // Add canvas to page
        const canvas = renderer.domElement;
        if (canvas) {
            canvas.style.position = 'absolute';
            canvas.style.top = '0';
            canvas.style.left = '0';
            canvas.style.zIndex = '5';

            // Try to find container, fallback to body
            const container = document.getElementById('container');
            if (container) {
                container.appendChild(canvas);
                console.log('Canvas added to container');
            } else {
                document.body.appendChild(canvas);
                console.log('Canvas added to body (container not found)');
            }
        } else {
            throw new Error('Failed to create canvas element');
        }
    
    // Create simple ground plane
    const groundGeometry = new THREE.PlaneGeometry(50, 50);
    const groundMaterial = new THREE.MeshBasicMaterial({ color: 0x4a5d23 });
    const ground = new THREE.Mesh(groundGeometry, groundMaterial);
    ground.rotation.x = -Math.PI / 2; // Make it horizontal
    scene.add(ground);
    console.log('Ground plane created');
    
        // Load character model
        loadCharacterModel();
    
    // Add some lighting
    const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
    scene.add(ambientLight);
    
    const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
    directionalLight.position.set(10, 10, 5);
    scene.add(directionalLight);
    console.log('Lighting added');
    
    // Setup controls
    setupControls();
    
        // Show game UI
        const gameCanvas = document.getElementById('gameCanvas');
        if (gameCanvas) gameCanvas.style.display = 'none'; // Hide the old canvas

        const gameUI = document.getElementById('gameUI');
        if (gameUI) gameUI.style.display = 'block';

        const crosshair = document.getElementById('crosshair');
        if (crosshair) crosshair.style.display = 'block';
    
    // Start animation loop
    animate();
    
    console.log('Game initialized successfully!');
    
        // Request pointer lock after a delay
        setTimeout(() => {
            document.body.requestPointerLock();
        }, 1000);

    } catch (error) {
        console.error('Error initializing game:', error);
        alert('Error starting game: ' + error.message);
    }
}

function setupControls() {
    // Keyboard controls
    document.addEventListener('keydown', onKeyDown);
    document.addEventListener('keyup', onKeyUp);
    document.addEventListener('mousemove', onMouseMove);
    
    // Pointer lock
    document.addEventListener('pointerlockchange', () => {
        pointerLocked = document.pointerLockElement === document.body;
    });
    
    console.log('Controls setup complete');
}

function onKeyDown(event) {
    switch (event.code) {
        case 'KeyW': keys.forward = true; break;
        case 'KeyS': keys.backward = true; break;
        case 'KeyA': keys.left = true; break;
        case 'KeyD': keys.right = true; break;
        case 'Space':
            event.preventDefault();
            keys.jump = true;
            break;
        case 'ShiftLeft':
        case 'ShiftRight':
            keys.run = true;
            break;
        case 'ControlLeft':
        case 'ControlRight':
            keys.crouch = true;
            break;
        case 'Escape':
            document.exitPointerLock();
            break;
    }
}

function onKeyUp(event) {
    switch (event.code) {
        case 'KeyW': keys.forward = false; break;
        case 'KeyS': keys.backward = false; break;
        case 'KeyA': keys.left = false; break;
        case 'KeyD': keys.right = false; break;
        case 'Space': keys.jump = false; break;
        case 'ShiftLeft':
        case 'ShiftRight': keys.run = false; break;
        case 'ControlLeft':
        case 'ControlRight': keys.crouch = false; break;
    }
}

function onMouseMove(event) {
    if (!pointerLocked) return;
    
    const sensitivity = 0.002;
    cameraYaw -= event.movementX * sensitivity;
    cameraPitch -= event.movementY * sensitivity;
    
    // Clamp pitch
    cameraPitch = Math.max(-Math.PI/3, Math.min(Math.PI/3, cameraPitch));
}

function updatePlayer(deltaTime) {
    if (!player || !isPlayerLoaded) return;

    // Movement
    const moveDirection = new THREE.Vector3();
    let speed = 3; // Base walking speed

    if (keys.crouch) {
        speed = 1.5; // Slower when crouching
    } else if (keys.run) {
        speed = 6; // Running speed
    }

    if (keys.forward) moveDirection.z -= 1;
    if (keys.backward) moveDirection.z += 1;
    if (keys.left) moveDirection.x -= 1;
    if (keys.right) moveDirection.x += 1;

    // Normalize and apply speed
    if (moveDirection.length() > 0) {
        moveDirection.normalize();

        // Apply camera rotation to movement
        moveDirection.applyAxisAngle(new THREE.Vector3(0, 1, 0), cameraYaw);

        playerVelocity.x = moveDirection.x * speed;
        playerVelocity.z = moveDirection.z * speed;

        // Rotate player to face movement direction
        if (player.rotation !== undefined) {
            const angle = Math.atan2(moveDirection.x, moveDirection.z);
            player.rotation.y = angle;
        }
    } else {
        playerVelocity.x = 0;
        playerVelocity.z = 0;
    }

    // Jumping
    if (keys.jump && isGrounded && !keys.crouch) {
        playerVelocity.y = 10;
        isGrounded = false;
        keys.jump = false; // Prevent continuous jumping
    }

    // Gravity
    if (!isGrounded) {
        playerVelocity.y -= 25 * deltaTime;
    }

    // Update position
    playerPosition.add(playerVelocity.clone().multiplyScalar(deltaTime));

    // Ground collision - fix the falling through ground issue
    const groundLevel = keys.crouch ? 0.5 : 1.0; // Character height when standing
    if (playerPosition.y <= groundLevel) {
        playerPosition.y = groundLevel; // Keep character above ground
        playerVelocity.y = 0;
        isGrounded = true;
    }

    // Update player mesh position
    player.position.copy(playerPosition);
}

function updateAnimations() {
    if (!isPlayerLoaded || !animations) return;

    let targetAnimation = 'idle';

    // Determine which animation to play based on movement state
    const isMoving = keys.forward || keys.backward || keys.left || keys.right;

    if (!isGrounded && playerVelocity.y > 0) {
        targetAnimation = 'jump_up';
    } else if (!isGrounded && playerVelocity.y < 0) {
        targetAnimation = 'jump_down';
    } else if (keys.crouch) {
        // For now, use idle when crouching (you can add crouch animations later)
        targetAnimation = 'idle';
    } else if (isMoving) {
        if (keys.run) {
            targetAnimation = 'run';
        } else {
            targetAnimation = 'walk';
        }
    }

    // Only change animation if it's different from current
    if (!currentAction || currentAction !== animations[targetAnimation]) {
        playAnimation(targetAnimation);
    }
}

function updateCamera() {
    if (!player) return;
    
    // Third person camera
    const cameraDistance = 8;
    const cameraHeight = 5;
    
    const cameraOffset = new THREE.Vector3(
        Math.sin(cameraYaw) * cameraDistance,
        cameraHeight + Math.sin(cameraPitch) * 3,
        Math.cos(cameraYaw) * cameraDistance
    );
    
    camera.position.copy(playerPosition).add(cameraOffset);
    camera.lookAt(playerPosition.clone().add(new THREE.Vector3(0, 2, 0)));
}

function updateUI() {
    const positionElement = document.getElementById('playerPosition');
    if (positionElement) {
        positionElement.textContent = `${playerPosition.x.toFixed(1)}, ${playerPosition.y.toFixed(1)}, ${playerPosition.z.toFixed(1)}`;
    }
}

// Character loading and animation functions
function loadCharacterModel() {
    console.log('Loading character model...');

    // Check if FBXLoader is available
    if (typeof THREE.FBXLoader === 'undefined') {
        console.error('FBXLoader not available! Check if the script loaded properly.');
        alert('FBXLoader not available - check console for details');
        createPlaceholderPlayer();
        return;
    }

    console.log('FBXLoader is available');

    const loader = new THREE.FBXLoader();

    // Try multiple possible paths for your character
    const possiblePaths = [
        'Assets/player/Player model/make_a_male_character_0717071834_texture_fbx/make_a_male_character_0717071834_texture_fbx/make_a_male_character_0717071834_texture.fbx',
        './Assets/player/Player model/make_a_male_character_0717071834_texture_fbx/make_a_male_character_0717071834_texture_fbx/make_a_male_character_0717071834_texture.fbx',
        'Assets\\player\\Player model\\make_a_male_character_0717071834_texture_fbx\\make_a_male_character_0717071834_texture_fbx\\make_a_male_character_0717071834_texture.fbx'
    ];

    let currentPathIndex = 0;

    function tryLoadPath(pathIndex) {
        if (pathIndex >= possiblePaths.length) {
            console.error('All paths failed, using placeholder');
            createPlaceholderPlayer();
            return;
        }

        const modelPath = possiblePaths[pathIndex];
        console.log(`Trying path ${pathIndex + 1}/${possiblePaths.length}:`, modelPath);

        loader.load(
            modelPath,
            (object) => {
                console.log('Character model loaded successfully!', object);
                player = object;

                // Fix rotation - make character stand upright
                // Try different rotation combinations to fix orientation
                console.log('Trying rotation fix...');

                // Test multiple rotation combinations to find the right one
                // Common FBX orientations:
                // Option 1: Rotate around Z-axis
                player.rotation.x = 0;
                player.rotation.y = 0;
                player.rotation.z = -Math.PI/2; // Try negative Z rotation

                console.log('Applied rotation: x=0, y=0, z=-π/2');

                // Scale the model appropriately
                player.scale.setScalar(0.02);

                // Position the character on the ground (not floating)
                playerPosition.y = 1.0; // Set initial height to ground level
                player.position.copy(playerPosition); // Update position

                // Enable shadows
                player.traverse((child) => {
                    if (child.isMesh) {
                        child.castShadow = true;
                        child.receiveShadow = true;
                        console.log('Mesh found:', child.name);
                    }
                });

                scene.add(player);
                console.log('Character added to scene and positioned correctly');

                // Setup animation mixer
                mixer = new THREE.AnimationMixer(player);

                // Load animations if available
                if (object.animations && object.animations.length > 0) {
                    console.log('Found animations:', object.animations.length);
                    loadAnimations();
                } else {
                    console.log('No animations found in FBX file');
                }

                isPlayerLoaded = true;
                console.log('Character setup complete - should be standing upright now!');
            },
            (progress) => {
                if (progress.total > 0) {
                    const percent = (progress.loaded / progress.total * 100).toFixed(1);
                    console.log(`Loading character model: ${percent}%`);
                }
            },
            (error) => {
                console.error(`Failed to load from path ${pathIndex + 1}: ${modelPath}`);
                console.error('Error details:', error);

                // Try next path
                tryLoadPath(pathIndex + 1);
            }
        );
    }

    // Start trying to load from the first path
    tryLoadPath(0);
}

function createPlaceholderPlayer() {
    console.log('Creating placeholder player...');
    const playerGeometry = new THREE.BoxGeometry(1, 2, 1);
    const playerMaterial = new THREE.MeshBasicMaterial({ color: 0x00ff00 });
    player = new THREE.Mesh(playerGeometry, playerMaterial);
    player.position.copy(playerPosition);
    player.castShadow = true;
    scene.add(player);
    isPlayerLoaded = true;
    console.log('Placeholder player created');
}

function loadAnimations() {
    console.log('Loading animations...');

    const animationPaths = {
        'idle': 'Assets/player/Animations/Character_movment/Breathing Idle 1.fbx', // Perfect idle animation!
        'walk': 'Assets/player/Animations/Character_movment/Walking.fbx',
        'run': 'Assets/player/Animations/Character_movment/Running.fbx',
        'jump_up': 'Assets/player/Animations/Character_movment/Jumping Up.fbx',
        'jump_down': 'Assets/player/Animations/Character_movment/Jumping Down.fbx',
        'crouch_to_stand': 'Assets/player/Animations/Character_movment/Crouch To Stand.fbx',
        'run_backward': 'Assets/player/Animations/Character_movment/Running Backward.fbx'
    };

    const loader = new THREE.FBXLoader();
    let loadedCount = 0;
    const totalAnimations = Object.keys(animationPaths).length;

    for (const [name, path] of Object.entries(animationPaths)) {
        loader.load(
            path,
            (animationFBX) => {
                if (animationFBX.animations.length > 0) {
                    const action = mixer.clipAction(animationFBX.animations[0]);
                    animations[name] = action;
                    console.log(`Animation loaded: ${name}`);
                }

                loadedCount++;
                if (loadedCount === totalAnimations) {
                    console.log('All animations loaded');
                    playAnimation('idle');
                }
            },
            undefined,
            (error) => {
                console.warn(`Failed to load animation ${name}:`, error);
                loadedCount++;
                if (loadedCount === totalAnimations) {
                    console.log('Animation loading complete (some failed)');
                    playAnimation('idle');
                }
            }
        );
    }
}

function playAnimation(name, loop = true) {
    if (!mixer || !animations[name]) return;

    // Fade out current animation
    if (currentAction) {
        currentAction.fadeOut(0.2);
    }

    // Fade in new animation
    currentAction = animations[name];
    currentAction.reset();
    currentAction.setLoop(loop ? THREE.LoopRepeat : THREE.LoopOnce);
    currentAction.fadeIn(0.2);
    currentAction.play();
}

function animate() {
    requestAnimationFrame(animate);

    const deltaTime = clock.getDelta();

    // Update animation mixer
    if (mixer) {
        mixer.update(deltaTime);
    }

    updatePlayer(deltaTime);
    updateCamera();
    updateUI();
    updateAnimations();

    renderer.render(scene, camera);
}

// Handle window resize
window.addEventListener('resize', () => {
    camera.aspect = window.innerWidth / window.innerHeight;
    camera.updateProjectionMatrix();
    renderer.setSize(window.innerWidth, window.innerHeight);
});
