// Simple Game Controller - Simplified Version
class Game {
    constructor() {
        this.scene = null;
        this.camera = null;
        this.renderer = null;
        this.player = null;
        this.clock = new THREE.Clock();
        this.isGameRunning = false;
        this.keys = {
            forward: false,
            backward: false,
            left: false,
            right: false,
            jump: false,
            run: false
        };
        this.playerPosition = new THREE.Vector3(0, 1, 0);
        this.playerVelocity = new THREE.Vector3(0, 0, 0);
        this.isGrounded = false;
    }

    init() {
        console.log('Initializing simple game...');

        this.setupRenderer();
        this.setupScene();
        this.setupCamera();
        this.setupLighting();
        this.createSimpleWorld();
        this.createSimplePlayer();
        this.setupControls();
        this.animate();

        this.isGameRunning = true;

        // Show game UI
        document.getElementById('gameCanvas').style.display = 'block';
        document.getElementById('gameUI').style.display = 'block';
        document.getElementById('crosshair').style.display = 'block';

        console.log('Simple game initialized successfully!');

        // Lock pointer for mouse look after a short delay
        setTimeout(() => {
            document.body.requestPointerLock();
        }, 500);
    }

    setupRenderer() {
        console.log('Setting up renderer...');
        try {
            this.renderer = new THREE.WebGLRenderer({
                antialias: true,
                alpha: true
            });
            this.renderer.setSize(window.innerWidth, window.innerHeight);
            this.renderer.setClearColor(0x87CEEB); // Sky blue
            this.renderer.shadowMap.enabled = true;

            const canvas = this.renderer.domElement;
            canvas.id = 'gameCanvas';
            document.getElementById('container').appendChild(canvas);
            console.log('Renderer created successfully');
        } catch (error) {
            console.error('Error creating renderer:', error);
        }
    }

    setupScene() {
        console.log('Setting up scene...');
        this.scene = new THREE.Scene();
        this.scene.background = new THREE.Color(0x87CEEB); // Sky blue

        // Add a test cube to make sure rendering is working
        const testGeometry = new THREE.BoxGeometry(2, 2, 2);
        const testMaterial = new THREE.MeshLambertMaterial({ color: 0xff0000 });
        const testCube = new THREE.Mesh(testGeometry, testMaterial);
        testCube.position.set(5, 1, 0);
        this.scene.add(testCube);
        console.log('Test cube added to scene');
    }

    setupCamera() {
        console.log('Setting up camera...');
        this.camera = new THREE.PerspectiveCamera(
            75,
            window.innerWidth / window.innerHeight,
            0.1,
            1000
        );
        this.camera.position.set(0, 10, 15);
        this.camera.lookAt(0, 0, 0);
        console.log('Camera positioned at:', this.camera.position);
    }

    setupLighting() {
        // Ambient light
        const ambientLight = new THREE.AmbientLight(0x404040, 0.4);
        this.scene.add(ambientLight);

        // Directional light (sun)
        const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
        directionalLight.position.set(50, 50, 50);
        directionalLight.castShadow = true;
        directionalLight.shadow.mapSize.width = 2048;
        directionalLight.shadow.mapSize.height = 2048;
        directionalLight.shadow.camera.near = 0.5;
        directionalLight.shadow.camera.far = 500;
        directionalLight.shadow.camera.left = -50;
        directionalLight.shadow.camera.right = 50;
        directionalLight.shadow.camera.top = 50;
        directionalLight.shadow.camera.bottom = -50;
        this.scene.add(directionalLight);
    }

    createSimpleWorld() {
        console.log('Creating simple world...');

        // Create ground plane
        const groundGeometry = new THREE.PlaneGeometry(100, 100);
        const groundMaterial = new THREE.MeshLambertMaterial({ color: 0x4a5d23 });
        const ground = new THREE.Mesh(groundGeometry, groundMaterial);
        ground.rotation.x = -Math.PI / 2;
        ground.receiveShadow = true;
        this.scene.add(ground);

        // Add some trees
        for (let i = 0; i < 10; i++) {
            const tree = this.createSimpleTree();
            tree.position.set(
                (Math.random() - 0.5) * 80,
                0,
                (Math.random() - 0.5) * 80
            );
            this.scene.add(tree);
        }

        console.log('Simple world created');
    }

    createSimpleTree() {
        const treeGroup = new THREE.Group();

        // Trunk
        const trunkGeometry = new THREE.CylinderGeometry(0.3, 0.5, 4, 8);
        const trunkMaterial = new THREE.MeshLambertMaterial({ color: 0x8B4513 });
        const trunk = new THREE.Mesh(trunkGeometry, trunkMaterial);
        trunk.position.y = 2;
        trunk.castShadow = true;
        treeGroup.add(trunk);

        // Leaves
        const leavesGeometry = new THREE.SphereGeometry(2.5, 8, 8);
        const leavesMaterial = new THREE.MeshLambertMaterial({ color: 0x228B22 });
        const leaves = new THREE.Mesh(leavesGeometry, leavesMaterial);
        leaves.position.y = 5;
        leaves.castShadow = true;
        treeGroup.add(leaves);

        return treeGroup;
    }

    createSimplePlayer() {
        console.log('Creating simple player...');

        // Create a simple green cylinder as player
        const playerGeometry = new THREE.CylinderGeometry(0.5, 0.5, 1.8, 8);
        const playerMaterial = new THREE.MeshLambertMaterial({ color: 0x00ff00 });
        this.player = new THREE.Mesh(playerGeometry, playerMaterial);
        this.player.position.copy(this.playerPosition);
        this.player.castShadow = true;
        this.scene.add(this.player);

        console.log('Simple player created');
    }

    setupControls() {
        // Keyboard controls
        document.addEventListener('keydown', (event) => this.onKeyDown(event));
        document.addEventListener('keyup', (event) => this.onKeyUp(event));

        // Mouse controls
        document.addEventListener('mousemove', (event) => this.onMouseMove(event));

        // Pointer lock
        document.addEventListener('pointerlockchange', () => {
            this.pointerLocked = document.pointerLockElement === document.body;
        });
    }

    onKeyDown(event) {
        switch (event.code) {
            case 'KeyW': this.keys.forward = true; break;
            case 'KeyS': this.keys.backward = true; break;
            case 'KeyA': this.keys.left = true; break;
            case 'KeyD': this.keys.right = true; break;
            case 'Space':
                event.preventDefault();
                this.keys.jump = true;
                break;
            case 'ShiftLeft':
            case 'ShiftRight':
                this.keys.run = true;
                break;
            case 'Escape':
                document.exitPointerLock();
                break;
        }
    }

    onKeyUp(event) {
        switch (event.code) {
            case 'KeyW': this.keys.forward = false; break;
            case 'KeyS': this.keys.backward = false; break;
            case 'KeyA': this.keys.left = false; break;
            case 'KeyD': this.keys.right = false; break;
            case 'Space': this.keys.jump = false; break;
            case 'ShiftLeft':
            case 'ShiftRight': this.keys.run = false; break;
        }
    }

    onMouseMove(event) {
        if (!this.pointerLocked) return;

        // Simple mouse look - rotate camera around player
        const sensitivity = 0.002;
        this.cameraYaw = (this.cameraYaw || 0) - event.movementX * sensitivity;
        this.cameraPitch = (this.cameraPitch || 0) - event.movementY * sensitivity;

        // Clamp pitch
        this.cameraPitch = Math.max(-Math.PI/3, Math.min(Math.PI/3, this.cameraPitch));
    }

    setupEventListeners() {
        window.addEventListener('resize', () => this.onWindowResize());
    }

    onWindowResize() {
        this.camera.aspect = window.innerWidth / window.innerHeight;
        this.camera.updateProjectionMatrix();
        this.renderer.setSize(window.innerWidth, window.innerHeight);
    }

    animate() {
        if (!this.isGameRunning) return;

        requestAnimationFrame(() => this.animate());

        const deltaTime = this.clock.getDelta();

        this.updatePlayer(deltaTime);
        this.updateCamera();
        this.updateUI();

        this.renderer.render(this.scene, this.camera);
    }

    updatePlayer(deltaTime) {
        if (!this.player) return;

        // Movement
        const moveDirection = new THREE.Vector3();
        const speed = this.keys.run ? 8 : 5;

        if (this.keys.forward) moveDirection.z -= 1;
        if (this.keys.backward) moveDirection.z += 1;
        if (this.keys.left) moveDirection.x -= 1;
        if (this.keys.right) moveDirection.x += 1;

        // Normalize and apply speed
        if (moveDirection.length() > 0) {
            moveDirection.normalize();

            // Apply camera rotation to movement
            const yaw = this.cameraYaw || 0;
            moveDirection.applyAxisAngle(new THREE.Vector3(0, 1, 0), yaw);

            this.playerVelocity.x = moveDirection.x * speed;
            this.playerVelocity.z = moveDirection.z * speed;
        } else {
            this.playerVelocity.x = 0;
            this.playerVelocity.z = 0;
        }

        // Jumping
        if (this.keys.jump && this.isGrounded) {
            this.playerVelocity.y = 10;
            this.isGrounded = false;
            this.keys.jump = false; // Prevent continuous jumping
        }

        // Gravity
        if (!this.isGrounded) {
            this.playerVelocity.y -= 25 * deltaTime;
        }

        // Update position
        this.playerPosition.add(this.playerVelocity.clone().multiplyScalar(deltaTime));

        // Ground collision
        if (this.playerPosition.y <= 0.9) {
            this.playerPosition.y = 0.9;
            this.playerVelocity.y = 0;
            this.isGrounded = true;
        }

        // Update player mesh position
        this.player.position.copy(this.playerPosition);
    }

    updateCamera() {
        if (!this.player) return;

        // Third person camera
        const cameraDistance = 8;
        const cameraHeight = 5;

        const yaw = this.cameraYaw || 0;
        const pitch = this.cameraPitch || 0;

        const cameraOffset = new THREE.Vector3(
            Math.sin(yaw) * cameraDistance,
            cameraHeight + Math.sin(pitch) * 3,
            Math.cos(yaw) * cameraDistance
        );

        this.camera.position.copy(this.playerPosition).add(cameraOffset);
        this.camera.lookAt(this.playerPosition.clone().add(new THREE.Vector3(0, 2, 0)));
    }

    updateUI() {
        if (this.player) {
            const pos = this.player.getPosition();
            const positionElement = document.getElementById('playerPosition');
            if (positionElement) {
                positionElement.textContent = `${pos.x.toFixed(1)}, ${pos.y.toFixed(1)}, ${pos.z.toFixed(1)}`;
            }
        }
    }

    destroy() {
        this.isGameRunning = false;
        if (this.renderer) {
            this.renderer.dispose();
        }
    }
}

// Global game instance
let game = null;

// Initialize game function called from menu
function initGame() {
    if (game) {
        game.destroy();
    }
    game = new Game();
    game.init();
}
