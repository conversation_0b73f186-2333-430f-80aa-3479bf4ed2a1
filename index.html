<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>LOST IN THE WOODS</title>
  <link href="https://fonts.googleapis.com/css2?family=Special+Elite&display=swap" rel="stylesheet">
  <!-- Latest Three.js -->
  <script src="https://cdn.jsdelivr.net/npm/three@0.149.0/build/three.min.js"></script>
  <!-- Required loaders -->
  <script src="https://cdn.jsdelivr.net/npm/three@0.149.0/examples/js/loaders/FBXLoader.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/three@0.149.0/examples/js/loaders/GLTFLoader.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/three@0.149.0/examples/js/controls/OrbitControls.js"></script>
  <!-- Required for FBX loading -->
  <script src="https://cdn.jsdelivr.net/npm/fflate@0.6.9/umd/index.js"></script>
  <!-- Load the simple game file -->
  <script src="js/game_simple.js"></script>
  <style>
    * { margin: 0; padding: 0; box-sizing: border-box; }
    html, body {
      width: 100%; height: 100%;
      background: black;
      font-family: 'Special Elite', serif;
      color: #fff;
      overflow: hidden;
    }
    #container {
      position: relative;
      width: 100%;
      height: 100%;
    }

    /* ✅ YouTube Background Embed FULLSCREEN ZOOM */
    .video-container {
      position: absolute;
      top: 0; left: 0;
      width: 100%;
      height: 100%;
      overflow: hidden;
      z-index: 1;
    }
    .video-container iframe {
      position: absolute;
      top: 50%; left: 50%;
      width: 120%; /* zoom slightly */
      height: 120%;
      transform: translate(-50%, -50%);
      pointer-events: none;
    }

    /* ✅ Audio Unlock Overlay */
    #audioUnlock {
      position: absolute;
      top: 0; left: 0;
      width: 100%; height: 100%;
      background: rgba(0,0,0,0.75);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 20;
      font-size: 2rem;
      text-align: center;
      color: #ddd;
      letter-spacing: 2px;
      cursor: pointer;
    }

    /* ✅ Menu Overlay */
    #menuOverlay {
      position: relative;
      z-index: 10;
      width: 100%; height: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      backdrop-filter: blur(1px);
    }

    #gameTitle {
      font-size: clamp(3rem, 6vw, 6rem);
      color: #d6d6d6;
      letter-spacing: 4px;
      text-shadow: 0 0 10px rgba(255,255,255,0.1);
      margin-bottom: 6vh;
      animation: flicker 3s infinite;
    }
    @keyframes flicker {
      0%,100% { opacity: 1; }
      30% { opacity: 0.9; }
      60% { opacity: 0.7; }
      90% { opacity: 1; }
    }

    #menuButtons {
      display: flex;
      flex-direction: column;
      gap: 20px;
      align-items: center;
    }
    .menu-button {
      font-size: clamp(1rem, 2vw, 1.8rem);
      background: rgba(0,0,0,0.4);
      border: 1px solid rgba(255,255,255,0.2);
      color: #ccc;
      padding: 12px 40px;
      cursor: pointer;
      letter-spacing: 2px;
      transition: all 0.3s ease;
      text-transform: uppercase;
    }
    .menu-button:hover {
      background: rgba(255,255,255,0.05);
      border-color: rgba(255,255,255,0.4);
      color: #fff;
      transform: scale(1.03);
    }

    /* ✅ Game Canvas */
    #gameCanvas {
      position: absolute;
      top: 0; left: 0;
      width: 100%; height: 100%;
      display: none;
      z-index: 5;
    }

    /* ✅ Game UI Overlay */
    #gameUI {
      position: absolute;
      top: 20px; left: 20px;
      z-index: 15;
      color: white;
      font-family: 'Special Elite', serif;
      display: none;
      background: rgba(0,0,0,0.3);
      padding: 15px;
      border-radius: 5px;
    }

    /* ✅ Crosshair */
    #crosshair {
      position: absolute;
      top: 50%; left: 50%;
      transform: translate(-50%, -50%);
      width: 20px; height: 20px;
      border: 2px solid rgba(255,255,255,0.8);
      border-radius: 50%;
      z-index: 15;
      display: none;
      pointer-events: none;
    }

    /* ✅ Controls Info */
    #controlsInfo {
      position: absolute;
      bottom: 20px; left: 20px;
      z-index: 15;
      color: rgba(255,255,255,0.8);
      font-family: 'Special Elite', serif;
      font-size: 14px;
      display: none;
      background: rgba(0,0,0,0.3);
      padding: 10px;
      border-radius: 5px;
    }

    /* ✅ Game Canvas */
    #gameCanvas {
      position: absolute;
      top: 0; left: 0;
      width: 100%; height: 100%;
      display: none;
      z-index: 5;
    }

    /* ✅ Game UI Overlay */
    #gameUI {
      position: absolute;
      top: 20px; left: 20px;
      z-index: 15;
      color: white;
      font-family: 'Special Elite', serif;
      display: none;
    }

    /* ✅ Crosshair */
    #crosshair {
      position: absolute;
      top: 50%; left: 50%;
      transform: translate(-50%, -50%);
      width: 20px; height: 20px;
      border: 2px solid rgba(255,255,255,0.8);
      border-radius: 50%;
      z-index: 15;
      display: none;
      pointer-events: none;
    }
  </style>
</head>
<body>
  <div id="container">

    <!-- ✅ ZOOMED VIDEO EMBED @ 38:17 -->
    <div class="video-container">
      <iframe id="bgVideo"
        src="https://www.youtube.com/embed/dxPgHs7CEQA?autoplay=1&loop=1&playlist=dxPgHs7CEQA&controls=0&modestbranding=1&start=2297&mute=1"
        frameborder="0" allow="autoplay; encrypted-media"></iframe>
    </div>

    <!-- ✅ Audio Unlock Overlay -->
    <div id="audioUnlock">
      CLICK TO ENABLE AUDIO
    </div>

    <!-- ✅ Menu Overlay -->
    <div id="menuOverlay">
      <h1 id="gameTitle">LOST IN THE WOODS</h1>
      <div id="menuButtons">
        <button class="menu-button" id="startButton">Enter the Forest</button>
        <button class="menu-button" id="storyButton">Story</button>
        <button class="menu-button" id="optionsButton">Options</button>
        <button class="menu-button" id="quitButton">Quit</button>
      </div>
    </div>

    <!-- ✅ Game UI Elements -->
    <div id="gameUI">
      <div>Health: <span id="healthBar">100</span></div>
      <div>Position: <span id="playerPosition">0, 0, 0</span></div>
    </div>

    <div id="crosshair"></div>

    <div id="controlsInfo">
      <div><strong>Controls:</strong></div>
      <div>WASD - Move</div>
      <div>Shift - Run</div>
      <div>Space - Jump</div>
      <div>Mouse - Look Around</div>
      <div>ESC - Release Mouse</div>
    </div>
  </div>

  <script>
    const audioUnlock = document.getElementById('audioUnlock');
    const bgVideo = document.getElementById('bgVideo');

    // ✅ Enable Audio after first click
    audioUnlock.addEventListener('click', () => {
      // Reload iframe with UNMUTED audio
      bgVideo.src = "https://www.youtube.com/embed/dxPgHs7CEQA?autoplay=1&loop=1&playlist=dxPgHs7CEQA&controls=0&modestbranding=1&start=2297";
      audioUnlock.style.display = "none";
    });

    // ✅ Start Game Button - Load the 3D World
    document.getElementById('startButton').addEventListener('click', () => {
      document.getElementById('menuOverlay').style.display = 'none';
      document.querySelector('.video-container').style.display = 'none';
      document.getElementById('controlsInfo').style.display = 'block';
      initGame();
    });
    document.getElementById('storyButton').addEventListener('click', () => {
      alert("The forest remembers every step you take...");
    });
    document.getElementById('optionsButton').addEventListener('click', () => {
      alert("Options menu coming soon...");
    });
    document.getElementById('quitButton').addEventListener('click', () => {
      alert("The woods won't let you leave. Close the tab manually.");
    });
  </script>
</body>
</html>
