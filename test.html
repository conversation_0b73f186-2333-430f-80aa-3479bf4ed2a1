<!DOCTYPE html>
<html>
<head>
    <title>Three.js Test</title>
    <style>
        body { margin: 0; padding: 0; background: black; }
        canvas { display: block; }
    </style>
</head>
<body>
    <script src="https://cdn.jsdelivr.net/npm/three@0.149.0/build/three.min.js"></script>
    <script>
        console.log('Starting Three.js test...');
        
        // Check if THREE is available
        if (typeof THREE === 'undefined') {
            console.error('THREE.js not loaded!');
            document.body.innerHTML = '<h1 style="color: white;">THREE.js failed to load</h1>';
        } else {
            console.log('THREE.js loaded successfully');
            
            // Create scene, camera, renderer
            const scene = new THREE.Scene();
            const camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
            const renderer = new THREE.WebGLRenderer();
            
            renderer.setSize(window.innerWidth, window.innerHeight);
            renderer.setClearColor(0x87CEEB); // Sky blue
            document.body.appendChild(renderer.domElement);
            
            // Create a simple cube
            const geometry = new THREE.BoxGeometry();
            const material = new THREE.MeshBasicMaterial({ color: 0x00ff00 });
            const cube = new THREE.Mesh(geometry, material);
            scene.add(cube);
            
            // Position camera
            camera.position.z = 5;
            
            // Animation loop
            function animate() {
                requestAnimationFrame(animate);
                cube.rotation.x += 0.01;
                cube.rotation.y += 0.01;
                renderer.render(scene, camera);
            }
            
            animate();
            console.log('Test scene created and running');
        }
    </script>
</body>
</html>
